<template>
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <text class="back" @click="goBack">＜</text>
  </view>
  <view class="login-container">
    <view class="bottom-popup">
      <view class="popup-header">请登录</view>
      <button
          v-if="next"
          class="login-btn"
          open-type="getPhoneNumber"
          @getphonenumber="onGetPhoneNumber"
      >
        手机号一键登录
      </button>

      <button
          v-if="!next"
          class="login-btn"
          @click="getUserInfo"
      >
        去登录
      </button>
    </view>
  </view>
</template>

<script setup>

import {ref} from "vue";
import {useUserStore} from "../../store";

const next = ref(false)

const userInfo = ref({})

const onGetPhoneNumber = async (e) => {
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const phoneCode = e.detail.code

    wx.login({
      success(res) {
        // 调用登录接口
        useUserStore().login({
          code: res.code,
          phoneCode: phoneCode,
          nickName: userInfo.value.nickName,
          avatarUrl: userInfo.value.avatarUrl
        }).then(() => {
          goBack()
        })
      },
    });

  } else {
    uni.showToast({ title: '您取消了授权', icon: 'none' })
    goBack()
  }

}

const getUserInfo = () => {
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success(res) {
      userInfo.value = res.userInfo
    },
  });

  next.value = true;
}

const goBack = () => {
  uni.navigateBack()
}

</script>

<style scoped>
.login-container {
  height: calc(93vh - 120px);
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.bottom-popup {
  background: #ffffff;
  padding: 40rpx 30rpx;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.popup-header {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.login-btn {
  background-color: #07c160;
  color: white;
  font-size: 32rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  text-align: center;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}
</style>
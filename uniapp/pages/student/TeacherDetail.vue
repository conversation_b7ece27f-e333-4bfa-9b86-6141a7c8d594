<template>
  <view class="teacher-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">人员详情页面</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 老师基本信息 -->
    <view class="teacher-info">
      <view class="teacher-header">
        <image class="avatar" :src="teacher.avatar" mode="aspectFill"></image>
        <view class="basic-info">
          <text class="name">{{ teacher.nickname }}</text>
          <text class="education">{{ getEducationLevelText(teacher.educationLevel) }}</text>
          <view class="status-row">
            <text class="verified-badge">已实名认证</text>
            <text class="tutored-count">已辅导：{{ teacher.tutoringCount }}</text>
          </view>
        </view>
      </view>

      <!-- 评分信息 -->
      <view class="rating-info">
        <view class="rating-item">
          <text class="rating-label">学员评分：</text>
          <text class="rating-value">{{ teacher.score }}分</text>
        </view>
        <view class="rating-item">
          <text class="rating-label">辅导人数：</text>
          <text class="rating-value">{{ teacher.tutoringCount }}人</text>
        </view>
<!--        <view class="rating-item">-->
<!--          <text class="rating-label">响应度：</text>-->
<!--          <text class="rating-value">{{ teacher.responseLevel }}</text>-->
<!--        </view>-->
      </view>
    </view>

    <!-- 自述 -->
    <view class="section">
      <view class="section-title">自述</view>
      <text class="section-content">{{ teacher.selfIntroduction }}</text>
    </view>

    <!-- 可辅导 -->
    <view class="section">
      <view class="section-title">可辅导</view>
      <view class="tags-grid">
        <text class="tag" v-for="(item, index) in teacher.goodAtItems" :key="index">{{ getGoodAtItemText(item) }}</text>
      </view>
    </view>

    <!-- 其他说明 -->
<!--    <view class="section">-->
<!--      <view class="section-title">其他说明：</view>-->
<!--      <text class="section-content">{{ teacher.otherInfo }}</text>-->
<!--    </view>-->

    <!-- 用户评价 -->
    <view class="section">
      <view class="section-title">用户评价：</view>
      <scroll-view
          class="review-list"
          scroll-y="true"
          :style="{ height: scrollHeight + 'px' }"
          @scrolltolower="loadMore"
          :lower-threshold="100"
      >
        <view class="review-item" v-for="(evaluation, index) in evaluationList" :key="index">
          <view class="review-header">
            <image class="reviewer-avatar" :src="evaluation.studentAvatar" mode="aspectFill"></image>
            <view class="review-info">
              <text class="reviewer-name">{{ evaluation.studentName }}</text>
              <text class="review-subject">{{ constants.TUTORING_ITEM_TEXT[evaluation.tutoringItem] }} | {{ evaluation.score }}分</text>
              <text class="review-date">{{ evaluation.evaluationDate }}</text>
            </view>
          </view>
          <text class="review-content">{{ evaluation.evaluation }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 右侧悬浮按钮 -->
<!--    <view class="floating-buttons">-->
<!--      <view class="float-btn contact-btn">-->
<!--        <text class="btn-text">可联系话、微信、</text>-->
<!--      </view>-->
<!--      <view class="float-btn tutor-btn">-->
<!--        <text class="btn-text">可辅导内容：论文</text>-->
<!--        <text class="btn-subtext">其他的可以自由沟</text>-->
<!--      </view>-->
<!--      <view class="float-btn review-btn">-->
<!--        <text class="btn-text">用户评价按照时间</text>-->
<!--      </view>-->
<!--    </view>-->

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
<!--      <view class="action-btn reject-btn" @click="handleReject">-->
<!--        <text class="action-btn-text">不合适</text>-->
<!--      </view>-->
      <view v-if="teacher.showCopyWechatBtn" class="action-btn copy-btn" @click="handleCopyWechatNo">
        <text class="action-btn-text">复制微信</text>
      </view>
      <view v-if="teacher.showConfirmTeacherBtn" class="action-btn confirm-btn" @click="handleConfirm">
        <text class="action-btn-text">确认老师</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {onMounted, ref} from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {teacherApi, constants, evaluationApi} from '../../../api'
import {handleApiResponse, handlePaginationData} from "../../../utils/apiHelper";

const teacher = ref({})
const orderId = ref()
const loading = ref(false)

const scrollHeight = ref(600)
const loadingMore = ref(false)
const evaluationList = ref([])
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
  hasMore: true
})

const goBack = () => {
  uni.navigateBack()
}

onLoad((options) => {
  if (options && options.teacherId) {
    // 根据teacherId获取老师详情
    loadTeacherDetail(options.teacherId)
    // 加载评价列表
    loadEvaluationList(options.teacherId)

    orderId.value = options.orderId
  }
})

const loadTeacherDetail = async (teacherId) => {
  if (!teacherId) return

  loading.value = true
  try {
    const response = await teacherApi.getTeacherDetail(teacherId)

    if (response.code === '1000') {
      teacher.value = response.result || {}
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载教师详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const loadEvaluationList = async (teacherId) => {
  if (!teacherId) return

  loading.value = true
  loadingMore.value = true

  const params = {
    current: pagination.value.current,
    size: pagination.value.size,
    teacherId: teacherId
  }

  const result = await handleApiResponse(
      evaluationApi.getEvaluationList(params),
      {
        showLoading: false,
        showError: true
      }
  )

  loading.value = false
  loadingMore.value = false

  if (result.success) {
    const paginationData = handlePaginationData(
        { result: result.data },
        evaluationList.value,
        false
    )

    evaluationList.value = paginationData.list
    pagination.value = paginationData.pagination
  }

}

// 加载更多数据
const loadMore = async () => {
  if (!pagination.value.hasMore || loadingMore.value) return

  pagination.value.current += 1
  await loadEvaluationList()
}

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 240 // 减去导航栏、标签页和tabbar高度
})

// 获取学历文本
const getEducationLevelText = (level) => {
  return constants.TEACHER_LEVEL_TEXT[level] || '未知'
}

// 获取擅长项目文本
const getGoodAtItemText = (item) => {
  return constants.GOOD_AT_ITEMS_TEXT[item]
}

// 底部按钮事件处理
const handleReject = () => {
  uni.showModal({
    title: '确认操作',
    content: '确定标记此老师为不合适吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '已标记为不合适',
          icon: 'success'
        })
        // 这里可以调用API标记老师为不合适
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

const handleCopyWechatNo = () => {
  const wechatNo = teacher.value.wechatNo || ''
  uni.setClipboardData({
    data: wechatNo,
    success: () => {
      uni.showToast({
        title: '已复制成功，可以添加微信，备注：来自辅导君',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}

const handleConfirm = () => {
  // 跳转到确认老师页面
  uni.navigateTo({
    url: '/uniapp/pages/student/ConfirmTeacher?teacherId=' + teacher.value.id + '&orderId=' + orderId.value
  })
}
</script>

<style scoped>
.teacher-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  position: relative;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 老师信息 */
.teacher-info {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.teacher-header {
  display: flex;
  margin-bottom: 32rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #f0f0f0;
  border: 4rpx solid #e0e0e0;
}

.basic-info {
  flex: 1;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.education {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.status-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.verified-badge {
  background: #ffa726;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.tutored-count {
  color: #007aff;
  font-size: 24rpx;
}

.rating-info {
  display: flex;
  justify-content: space-between;
}

.rating-item {
  display: flex;
  align-items: center;
}

.rating-label {
  font-size: 28rpx;
  color: #666;
}

.rating-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 通用区块样式 */
.section {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 标签网格 */
.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
}

/* 评价列表 */
.review-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.review-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
}

.review-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.review-header {
  display: flex;
  margin-bottom: 16rpx;
}

.reviewer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background: #f0f0f0;
  border: 2rpx solid #e0e0e0;
}

.review-info {
  flex: 1;
}

.reviewer-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 4rpx;
}

.review-subject {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.review-date {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.review-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 右侧悬浮按钮 */
.floating-buttons {
  position: fixed;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  z-index: 100;
}

.float-btn {
  background: #ffd54f;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  min-width: 200rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  display: block;
}

.btn-subtext {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-top: 4rpx;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #fff;
  padding: 105rpx 32rpx 20rpx 32rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #eee;
  gap: 16rpx;
  z-index: 200;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  font-weight: 500;
}

.reject-btn {
  background: #f5f5f5;
  border: 1rpx solid #ddd;
}

.reject-btn .action-btn-text {
  color: #666;
  font-size: 32rpx;
}

.copy-btn {
  background: #42a5f5;
}

.copy-btn .action-btn-text {
  color: #fff;
  font-size: 32rpx;
}

.confirm-btn {
  background: #42a5f5;
}

.confirm-btn .action-btn-text {
  color: #fff;
  font-size: 32rpx;
}
</style>

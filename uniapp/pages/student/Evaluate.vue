<template>
  <view class="evaluate">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">评价老师</text>
      <view class="nav-placeholder"></view>
    </view>
    
    <!-- 老师信息 -->
    <view class="teacher-card">
      <image class="teacher-avatar" :src="teacherInfo.avatar" mode="aspectFill" />
      <view class="teacher-info">
        <text class="teacher-name">{{ teacherInfo.name }}</text>
        <text class="teacher-school">{{ teacherInfo.school }} | {{ teacherInfo.degree }}</text>
        <text class="teacher-major">{{ teacherInfo.major }}</text>
      </view>
    </view>
    
    <!-- 评分区域 -->
    <view class="rating-section">
      <view class="rating-item">
        <text class="rating-label">总体评价</text>
        <view class="rating-right">
          <view class="stars">
            <text
              v-for="i in 5"
              :key="'overall-' + i"
              class="star"
              :class="{ active: i <= evaluation.overall, half: i === Math.ceil(evaluation.overall) && evaluation.overall % 1 !== 0 }"
              @click="setRating('overall', i)"
            >
              ★
            </text>
          </view>
          <text class="rating-score">{{ evaluation.overall.toFixed(1) }}分</text>
        </view>
      </view>

      <view class="rating-item">
        <text class="rating-label">专业质量</text>
        <view class="rating-right">
          <view class="stars">
            <text
              v-for="i in 5"
              :key="'professional-' + i"
              class="star"
              :class="{ active: i <= evaluation.professional, half: i === Math.ceil(evaluation.professional) && evaluation.professional % 1 !== 0 }"
              @click="setRating('professional', i)"
            >
              ★
            </text>
          </view>
          <text class="rating-score">{{ evaluation.professional.toFixed(1) }}分</text>
        </view>
      </view>

      <view class="rating-item">
        <text class="rating-label">服务态度</text>
        <view class="rating-right">
          <view class="stars">
            <text
              v-for="i in 5"
              :key="'attitude-' + i"
              class="star"
              :class="{ active: i <= evaluation.attitude, half: i === Math.ceil(evaluation.attitude) && evaluation.attitude % 1 !== 0 }"
              @click="setRating('attitude', i)"
            >
              ★
            </text>
          </view>
          <text class="rating-score">{{ evaluation.attitude.toFixed(1) }}分</text>
        </view>
      </view>
    </view>
    
    <!-- 标签评价 -->
    <view class="tags-section">
      <view class="section-title">服务标签</view>
      <view class="tags-grid">
        <view 
          v-for="tag in tagOptions" 
          :key="tag" 
          class="tag-item" 
          :class="{ active: evaluation.tags.includes(tag) }"
          @click="toggleTag(tag)"
        >
          {{ tag }}
        </view>
      </view>
    </view>
    
    <!-- 文字评价 -->
    <view class="comment-section">
      <view class="section-title">评价说明</view>
      <textarea
        v-model="evaluation.comment"
        placeholder="请客观评价（300字），您的评价有助于其他用户参考，感谢您的评价！"
        class="comment-textarea"
        maxlength="300"
      />
      <view class="char-count">{{ evaluation.comment.length }}/300</view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitEvaluation" :disabled="!canSubmit">
        提交评价
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const teacherInfo = ref({
  name: '张伟',
  school: '清华大学',
  degree: '博士',
  major: '环境会计',
  avatar: '/static/logo.png'
})

const evaluation = ref({
  attitude: 4.5,
  professional: 4.5,
  overall: 4.5,
  tags: [],
  comment: ''
})

const tagOptions = [
  '专业负责', '耐心细致', '响应及时', '讲解清晰',
  '经验丰富', '态度友好', '效率很高', '物超所值',
  '推荐给朋友', '下次还会选择'
]

const canSubmit = computed(() => {
  return evaluation.value.attitude > 0 &&
         evaluation.value.professional > 0 &&
         evaluation.value.overall > 0
})

const setRating = (type, rating) => {
  evaluation.value[type] = rating
}

const toggleTag = (tag) => {
  const index = evaluation.value.tags.indexOf(tag)
  if (index > -1) {
    evaluation.value.tags.splice(index, 1)
  } else {
    evaluation.value.tags.push(tag)
  }
}

const goBack = () => {
  // 返回到订单列表页面，跳过订单详情页面
  uni.navigateBack({
    delta: 2 // 返回两级页面，直接回到订单列表
  })
}

const submitEvaluation = () => {
  if (!canSubmit.value) {
    uni.showToast({ 
      title: '请完成所有评分', 
      icon: 'none' 
    })
    return
  }
  
  uni.showModal({
    title: '确认提交',
    content: '评价提交后不可修改，确定要提交吗？',
    success: (res) => {
      if (res.confirm) {
        // 这里可以调用API提交评价
        uni.showToast({ 
          title: '评价提交成功', 
          icon: 'success' 
        })
        
        setTimeout(() => {
          uni.navigateBack({
            delta: 2 // 返回两级页面，直接回到订单列表
          })
        }, 1500)
      }
    }
  })
}

onLoad((options) => {
  // 可以从参数中获取订单ID和老师信息
  if (options && options.orderId) {
    // 根据订单ID获取老师信息
  }
})
</script>

<style scoped>
.evaluate {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-placeholder {
  width: 48rpx;
  height: 48rpx;
}

/* 老师信息卡片 */
.teacher-card {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.teacher-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #eee;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.teacher-school {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.teacher-major {
  font-size: 24rpx;
  color: #999;
}

/* 评分区域 */
.rating-section, .tags-section, .comment-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.rating-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 16rpx 0;
}

.rating-item:last-child {
  margin-bottom: 0;
}

.rating-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.rating-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 32rpx;
  color: #ddd;
  transition: color 0.2s;
  cursor: pointer;
}

.star.active {
  color: #ff6b35;
}

.star.half {
  color: #ff6b35;
}

.rating-score {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  min-width: 60rpx;
}

/* 标签评价 */
.tags-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  background: #f8f8f8;
  color: #666;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.tag-item.active {
  background: #e3f2fd;
  color: #1e98d7;
  border-color: #1e98d7;
}

/* 文字评价 */
.comment-textarea {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
  line-height: 1.5;
}

.comment-textarea:focus {
  border-color: #1e98d7;
  background: #fff;
}

.char-count {
  text-align: right;
  color: #999;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  background: #1e98d7;
  color: #fff;
  border: none;
  font-weight: bold;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
}
</style>

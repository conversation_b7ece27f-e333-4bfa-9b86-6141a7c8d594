<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">需求详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="content" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="info-item">
          <text class="info-label">发布时间</text>
          <text class="info-value">{{ order.publishTime || '2025-3-12 12:11:12' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">辅导项目</text>
          <text class="info-value">{{ order.category || '作业' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习阶段</text>
          <text class="info-value">{{ order.stage || '本科' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习位置</text>
          <text class="info-value">{{ order.location || '国内' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习专业</text>
          <text class="info-value">{{ order.major || '会计' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">预算区间</text>
          <text class="info-value">{{ order.budget || '10000-20000元' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">老师学历</text>
          <text class="info-value">{{ order.teacherDegree || '博士' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">老师是否留学</text>
          <text class="info-value">{{ order.teacherAbroad || '不要求' }}</text>
        </view>
      </view>

      <!-- 需求描述 -->
      <view class="description-section">
        <view class="section-title">需求描述</view>
        <view class="description-content">
          {{ order.description || '想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师' }}
        </view>
      </view>

      <!-- 其他说明 -->
      <view class="other-section">
        <view class="section-title">其他说明</view>
        <view class="other-content">
          {{ order.otherInfo || '希望老师有情有专业，有耐心希望老师有情有专业，有耐心希望老师有情有专业，有耐心希望老师有情有专业，有耐心希望老师有情有专业，有耐心。' }}
        </view>
      </view>

      <!-- 报名状态 -->
      <view class="signup-status">
        <text class="signup-text">已有{{ order.signupCount || 20 }}人投递</text>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="bottom-action">
      <button class="apply-btn" @click="applyOrder" :disabled="order.status === 'applied'">
        {{ order.status === 'applied' ? '已投递' : '投递' }}
      </button>
    </view>

    <!-- 投递成功提示 -->
    <view class="success-tip" v-if="showSuccessTip">
      <view class="tip-content">
        <text class="tip-text">投递后返回首页，给一个toast提示：投递成功</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const scrollHeight = ref(600)
const showSuccessTip = ref(false)

// 订单信息
const order = ref({
  id: '',
  publishTime: '',
  category: '',
  stage: '',
  location: '',
  major: '',
  budget: '',
  teacherDegree: '',
  teacherAbroad: '',
  description: '',
  otherInfo: '',
  signupCount: 20,
  status: 'pending' // pending, applied
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 投递订单
const applyOrder = () => {
  if (order.value.status === 'applied') {
    return
  }

  uni.showModal({
    title: '确认投递',
    content: '确定要投递这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        // 更新订单状态
        order.value.status = 'applied'
        
        // 显示成功提示
        uni.showToast({
          title: '投递成功',
          icon: 'success',
          duration: 2000
        })

        // 延迟返回首页
        setTimeout(() => {
          uni.navigateBack()
        }, 2000)
      }
    }
  })
}

onLoad((options) => {
  if (options && options.order) {
    // 接收传递的订单数据
    const orderData = JSON.parse(decodeURIComponent(options.order))
    order.value = { ...order.value, ...orderData }
  }
})

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 200 // 减去导航栏和底部按钮高度
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 0 32rpx;
}

/* 基本信息 */
.info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #666;
}

/* 描述和其他说明 */
.description-section,
.other-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.description-content,
.other-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 报名状态 */
.signup-status {
  text-align: center;
  padding: 32rpx;
  margin-top: 24rpx;
}

.signup-text {
  font-size: 28rpx;
  color: #f57c00;
  background: #fff3e0;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
}

/* 底部操作按钮 */
.bottom-action {
  padding: 24rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.apply-btn {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.apply-btn:disabled {
  background: #ccc;
  color: #999;
}

/* 成功提示 */
.success-tip {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #ffeb3b;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  z-index: 1000;
}

.tip-content {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #333;
}
</style>

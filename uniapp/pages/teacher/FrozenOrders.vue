<template>
  <view class="frozen-orders">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">我的订单</text>
      <view class="nav-right">
        <text class="nav-dots">⋯</text>
        <text class="nav-help">?</text>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <view class="orders-list">
      <view 
        v-for="(order, index) in frozenOrders" 
        :key="index"
        class="order-item"
        @click="viewOrderDetail(order)"
      >
        <!-- 订单头部 -->
        <view class="order-header">
          <text class="order-number">订单号：{{ order.orderNumber }}</text>
          <text class="order-time">{{ order.createTime }}</text>
        </view>
        
        <!-- 订单内容 -->
        <view class="order-content">
          <view class="order-row">
            <text class="order-label">辅导项目：</text>
            <view class="subject-tag" :class="order.name.toLowerCase()">
              <text class="subject-text">{{ order.subject }}</text>
            </view>
            <view class="order-right">
              <view class="status-tag frozen">
                <text class="status-text">冻结中</text>
              </view>
              <text class="order-amount">总金额：{{ order.amount }}元</text>
            </view>
          </view>
          
          <view class="order-row">
            <text class="order-label">交付阶段：</text>
            <text class="order-value">{{ order.deliveryStage }}</text>
            <view class="order-right">
              <text class="student-label">学生：</text>
              <text class="student-name">{{ order.studentName }}</text>
            </view>
          </view>
          
          <view class="order-row">
            <text class="order-label">完成时间：</text>
            <text class="order-value">{{ order.completeTime }}</text>
            <view class="order-right">
              <view class="rating-tag">
                <text class="rating-text">{{ order.rating }}分</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 冻结订单数据
const frozenOrders = ref([
  {
    orderNumber: 'JS13455555333',
    createTime: '2025-3-12 12:11:12',
    subject: '作业',
    name: 'zuoye',
    deliveryStage: '已完成',
    studentName: '张菲菲',
    completeTime: '2025-4-12 12:11:11',
    amount: '2000',
    rating: '4.5'
  },
  {
    orderNumber: 'JS13455555333',
    createTime: '2025-3-12 12:11:12',
    subject: '论文',
    name: 'lunwen',
    deliveryStage: '已完成',
    studentName: '张菲菲',
    completeTime: '2025-4-12 12:11:11',
    amount: '3000',
    rating: '4.5'
  }
])

const goBack = () => {
  uni.navigateBack()
}

const viewOrderDetail = (order) => {
  // 这里可以跳转到订单详情页面
  uni.showToast({
    title: '订单详情功能开发中',
    icon: 'none'
  })
}
</script>

<style scoped>
.frozen-orders {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-dots {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-help {
  font-size: 32rpx;
  color: #333;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 订单列表 */
.orders-list {
  padding: 32rpx;
}

.order-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.order-header {
  background: #333;
  color: #fff;
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 28rpx;
  font-weight: bold;
}

.order-time {
  font-size: 24rpx;
  opacity: 0.8;
}

.order-content {
  padding: 32rpx;
}

.order-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}

.order-row:last-child {
  margin-bottom: 0;
}

.order-label {
  font-size: 28rpx;
  color: #333;
  min-width: 160rpx;
}

.order-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.order-right {
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 科目标签 */
.subject-tag {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.subject-tag.zuoye {
  background: #e3f2fd;
  color: #1976d2;
}

.subject-tag.lunwen {
  background: #f3e5f5;
  color: #7b1fa2;
}

.subject-text {
  font-size: 24rpx;
}

/* 状态标签 */
.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.status-tag.frozen {
  background: #fff3cd;
  color: #856404;
}

.status-text {
  font-size: 24rpx;
}

.order-amount {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.student-label {
  font-size: 28rpx;
  color: #666;
}

.student-name {
  font-size: 28rpx;
  color: #333;
}

/* 评分标签 */
.rating-tag {
  background: #ff9800;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
}

.rating-text {
  font-size: 24rpx;
  font-weight: bold;
}
</style>

<template>
  <view class="wallet">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">我的钱包</text>
      <view class="nav-placeholder"></view>
    </view>
    
    <!-- 总额卡片 -->
    <view class="balance-card">
      <view class="total-section">
        <text class="total-label">总额：{{ walletInfo.totalAmount }}</text>
      </view>
      <view class="balance-details">
        <view class="balance-item">
          <text class="balance-label">已提现：{{ walletInfo.withdrawn }}</text>
        </view>
        <view class="balance-item">
          <text class="balance-label">未提现：{{ walletInfo.pending }}</text>
        </view>
      </view>
      <view class="withdraw-section">
        <button class="withdraw-btn" @click="goWithdraw">提现</button>
      </view>
    </view>

    <!-- 提现记录 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">提现记录</text>
      </view>
      
      <view class="records-list">
        <view 
          v-for="(record, index) in withdrawRecords" 
          :key="index"
          class="record-item"
        >
          <view class="record-info">
            <view class="record-header">
              <text class="record-time">申请时间：{{ record.applyTime }}</text>
              <view class="record-status" :class="record.status">
                <text class="status-text">{{ getStatusText(record.status) }}</text>
              </view>
            </view>
            <text class="record-amount">提现金额：{{ record.amount }}元</text>
            <text class="record-actual">实际到账：{{ record.actualAmount }}元</text>
            <text class="record-method">提现方式：{{ record.method }}</text>
            <text v-if="record.completeTime" class="record-complete">
              打款时间：{{ record.completeTime }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 钱包信息
const walletInfo = ref({
  totalAmount: '10,000',
  withdrawn: '9,000',
  pending: '1,000'
})

// 提现记录
const withdrawRecords = ref([
  {
    applyTime: '2025-11-11 12:12:12',
    amount: '500',
    actualAmount: '450',
    method: '微信',
    status: 'processing'
  },
  {
    applyTime: '2025-11-11 12:12:12',
    completeTime: '2025-11-12 12:12:12',
    amount: '500',
    actualAmount: '450',
    method: '微信',
    status: 'completed'
  }
])

const getStatusText = (status) => {
  const statusMap = {
    'processing': '提现中',
    'completed': '已打款',
    'failed': '提现失败'
  }
  return statusMap[status] || '未知状态'
}

const goBack = () => {
  uni.navigateBack()
}

const goWithdraw = () => {
  uni.navigateTo({
    url: './Withdraw'
  })
}
</script>

<style scoped>
.wallet {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-placeholder {
  width: 72rpx;
}

/* 总额卡片 */
.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  color: #fff;
}

.total-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.total-label {
  font-size: 48rpx;
  font-weight: bold;
}

.balance-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.balance-item {
  flex: 1;
  text-align: center;
}

.balance-label {
  font-size: 32rpx;
}

.withdraw-section {
  text-align: right;
}

.withdraw-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid #fff;
  border-radius: 48rpx;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 48rpx;
  line-height: 1;
}

/* 提现记录 */
.records-section {
  margin: 32rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.records-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.record-item {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-time {
  font-size: 28rpx;
  color: #666;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
}

.record-status.processing {
  background: #fff3cd;
  color: #856404;
}

.record-status.completed {
  background: #d4edda;
  color: #155724;
}

.record-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.status-text {
  font-size: 24rpx;
}

.record-amount,
.record-actual,
.record-method,
.record-complete {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.record-complete {
  color: #666;
}
</style>

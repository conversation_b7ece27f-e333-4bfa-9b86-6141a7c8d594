<template>
  <view class="profile-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="nav-back" @click="goBack">＜</text>
      <text class="nav-title">我的</text>
      <view class="nav-right">
        <text class="nav-dots">⋯</text>
        <text class="nav-help">?</text>
      </view>
    </view>

    <!-- 用户信息 -->
    <view class="user-info">
      <view class="user-avatar">
        <image v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar-image" />
        <text v-else class="avatar-text">头像</text>
      </view>
      <text class="user-name">{{ userInfo.realName || userInfo.name || '用户' }}</text>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToPage('wallet')">
        <text class="menu-text">我的钱包</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToPage('orders')">
        <text class="menu-text">我的订单</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToPage('info')">
        <text class="menu-text">我的基本信息</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToPage('tutor')">
        <text class="menu-text">我擅长的辅导</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" @click="goToPage('role')">
        <text class="menu-text">我的角色</text>
        <text class="menu-right">
          <text class="role-text">辅导老师</text>
          <text class="menu-arrow">></text>
        </text>
      </view>
      <view class="menu-item" @click="goToPage('service')">
        <text class="menu-text">在线客服</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '../../../store/user.js'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

const goBack = () => {
  uni.navigateBack()
}

const goToPage = (page) => {
  switch (page) {
    case 'orders':
      uni.navigateTo({
        url: './Orders'
      })
      break
    case 'wallet':
      uni.navigateTo({
        url: './Wallet'
      })
      break
    case 'info':
      uni.navigateTo({
        url: './PersonalInfo'
      })
      break
    case 'tutor':
      uni.navigateTo({
        url: './TutorSkills'
      })
      break
    case 'role':
    case 'service':
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
      break
  }
}
</script>

<style scoped>
.profile-page {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 105rpx 32rpx 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-dots {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-help {
  font-size: 32rpx;
  color: #333;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 用户信息 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx 40rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-text {
  font-size: 24rpx;
  color: #999;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 功能菜单 */
.menu-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.role-text {
  font-size: 28rpx;
  color: #666;
}

.menu-arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 底部提示 */
.bottom-tips {
  margin-top: auto;
  padding: 40rpx 32rpx;
}

.tip-item {
  background: #ffebf0;
  padding: 24rpx;
  border-radius: 12rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
</style>
